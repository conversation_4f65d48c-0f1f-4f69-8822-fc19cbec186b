// shopifyGraphQL.js - Utility for Shopify GraphQL API operations

import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config();

// Shopify GraphQL API endpoint
const SHOPIFY_GRAPHQL_URL = process.env.SHOPIFY_ENDPOINT;
const SHOPIFY_ACCESS_TOKEN = process.env.SHOPIFY_ACCESS_TOKEN;

/**
 * Execute a GraphQL query against the Shopify API
 * @param {string} query - The GraphQL query to execute
 * @param {Object} variables - Variables for the GraphQL query
 * @returns {Promise<Object>} - The query result
 */
export async function executeShopifyGraphQL(query, variables = {}) {
  if (!SHOPIFY_GRAPHQL_URL || !SHOPIFY_ACCESS_TOKEN) {
    throw new Error('Shopify GraphQL URL or access token not configured in environment variables');
  }

  try {
    console.log(`Executing GraphQL query with variables: ${JSON.stringify(variables)}`);

    const response = await fetch(SHOPIFY_GRAPHQL_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN
      },
      body: JSON.stringify({
        query,
        variables
      })
    });

    const result = await response.json();

    console.log(`GraphQL response: ${JSON.stringify(result)}`);

    if (result.errors) {
      console.error('GraphQL Errors:', JSON.stringify(result.errors, null, 2));
      throw new Error(`GraphQL Error: ${result.errors[0].message}`);
    }

    return result.data;
  } catch (error) {
    console.error('Error executing Shopify GraphQL query:', error);
    throw error;
  }
}

/**
 * Set inventory quantity to a specific value for an inventory item
 * @param {string} inventoryItemId - The Shopify inventory item ID
 * @param {number} quantity - The quantity to set (0 or 1 for discs)
 * @returns {Promise<Object>} - The result of the operation
 */
export async function setInventoryItemQuantity(inventoryItemId, quantity) {
  try {
    console.log(`Setting inventory to ${quantity} for inventory item ${inventoryItemId}`);

    // Format the inventory item ID if needed
    let formattedInventoryItemId = inventoryItemId;
    if (!String(inventoryItemId).startsWith('gid://shopify/InventoryItem/')) {
      formattedInventoryItemId = `gid://shopify/InventoryItem/${inventoryItemId}`;
      console.log(`Formatted inventory item ID: ${formattedInventoryItemId}`);
    }

    // Get the location ID from environment variables or use the default one
    let locationId = process.env.SHOPIFY_LOCATION_ID;
    if (!locationId) {
      console.log('Shopify location ID not found in environment variables, using default location');
      // Use the Drop Zone Disc Golf LFK Retail Shop location as default
      locationId = 'gid://shopify/Location/63618220220';
    }

    try {
      // Try using GraphQL API first
      console.log(`Attempting to set inventory to ${quantity} using GraphQL API`);

      // Construct the inventory level ID
      const inventoryLevelId = `gid://shopify/InventoryLevel/${formattedInventoryItemId.replace('gid://shopify/InventoryItem/', '')}?location_id=${locationId.replace('gid://shopify/Location/', '')}`;

      // Query using the inventory level ID
      const getInventoryLevelQuery = `
        query getInventoryLevel($id: ID!) {
          inventoryLevel(id: $id) {
            quantities(names: "available") {
              name
              quantity
            }
          }
        }
      `;

      const getInventoryLevelVariables = {
        id: inventoryLevelId
      };

      console.log(`Getting current inventory level for inventoryLevelId: ${inventoryLevelId}`);
      const inventoryLevelResult = await executeShopifyGraphQL(getInventoryLevelQuery, getInventoryLevelVariables);

      let currentQuantity = 0;
      if (inventoryLevelResult.inventoryLevel &&
          inventoryLevelResult.inventoryLevel.quantities &&
          inventoryLevelResult.inventoryLevel.quantities.length > 0) {
        // Find the 'available' quantity
        const availableQuantity = inventoryLevelResult.inventoryLevel.quantities.find(q => q.name === 'available');
        if (availableQuantity) {
          currentQuantity = availableQuantity.quantity;
        }
      }

      console.log(`Current inventory quantity: ${currentQuantity}`);

      // If the quantity is already at the target value, no need to update
      if (currentQuantity === quantity) {
        console.log(`Inventory quantity already at ${quantity}, no update needed`);
        return {
          success: true,
          inventoryItemId: formattedInventoryItemId,
          newQuantity: quantity,
          message: `Inventory already at ${quantity}, no update needed`
        };
      }

      // Calculate the delta (positive or negative value to adjust inventory)
      const delta = quantity - currentQuantity;

      // Use the inventoryBulkAdjustQuantityAtLocation mutation to set the inventory to the target quantity
      const adjustQuantityQuery = `
        mutation inventoryBulkAdjustQuantityAtLocation($inventoryItemId: ID!, $locationId: ID!, $delta: Int!) {
          inventoryBulkAdjustQuantityAtLocation(
            inventoryItemAdjustments: [
              {
                inventoryItemId: $inventoryItemId,
                availableDelta: $delta
              }
            ],
            locationId: $locationId
          ) {
            inventoryLevels {
              quantities(names: "available") {
                name
                quantity
              }
              inventoryItem {
                id
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const adjustQuantityVariables = {
        inventoryItemId: formattedInventoryItemId,
        locationId,
        delta
      };

      console.log(`Setting inventory to ${quantity} using inventoryBulkAdjustQuantityAtLocation mutation with delta: ${delta}`);
      const result = await executeShopifyGraphQL(adjustQuantityQuery, adjustQuantityVariables);

      if (result.inventoryBulkAdjustQuantityAtLocation.userErrors && result.inventoryBulkAdjustQuantityAtLocation.userErrors.length > 0) {
        const errorMessage = result.inventoryBulkAdjustQuantityAtLocation.userErrors.map(err => `${err.field}: ${err.message}`).join(', ');
        throw new Error(`Error setting inventory to ${quantity}: ${errorMessage}`);
      }

      console.log(`Successfully set inventory to ${quantity} for inventory item ${formattedInventoryItemId} using GraphQL API`);

      return {
        success: true,
        inventoryItemId: formattedInventoryItemId,
        newQuantity: quantity,
        api: 'graphql',
        result: result.inventoryBulkAdjustQuantityAtLocation
      };
    } catch (graphqlError) {
      // If GraphQL API fails, try using REST API as fallback
      console.error(`GraphQL API failed: ${graphqlError.message}. Falling back to REST API.`);

      // Import the REST API utility
      const { setInventoryItemQuantityREST } = await import('./shopifyREST.js');

      // Set the inventory to the target quantity using REST API
      console.log(`Attempting to set inventory to ${quantity} using REST API`);
      const restResult = await setInventoryItemQuantityREST(formattedInventoryItemId, quantity);

      console.log(`Successfully set inventory to ${quantity} for inventory item ${formattedInventoryItemId} using REST API`);

      return {
        ...restResult,
        api: 'rest'
      };
    }
  } catch (error) {
    console.error(`Error setting inventory to ${quantity} for inventory item ${inventoryItemId}:`, error);
    throw error;
  }
}

/**
 * Set inventory quantity to zero for an inventory item directly
 * @param {string} inventoryItemId - The Shopify inventory item ID
 * @returns {Promise<Object>} - The result of the operation
 */
export async function setInventoryItemToZero(inventoryItemId) {
  return setInventoryItemQuantity(inventoryItemId, 0);
}

/**
 * Get inventory item ID for a product variant
 * @param {string} productId - The product ID (gid://shopify/Product/1234567890)
 * @returns {Promise<Object>} - The inventory item ID and variant ID
 */
export async function getInventoryItemIdForProduct(productId) {
  const formattedProductId = formatProductId(productId);
  console.log(`Getting inventory item ID for product ${formattedProductId}`);

  const query = `
    query getProductVariants($productId: ID!) {
      product(id: $productId) {
        title
        variants(first: 1) {
          edges {
            node {
              id
              title
              inventoryItem {
                id
                tracked
              }
            }
          }
        }
      }
    }
  `;

  const variables = {
    productId: formattedProductId
  };

  try {
    const result = await executeShopifyGraphQL(query, variables);

    if (!result.product) {
      throw new Error(`Product not found: ${formattedProductId}`);
    }

    if (!result.product.variants.edges.length) {
      throw new Error(`No variants found for product ${formattedProductId}`);
    }

    const variant = result.product.variants.edges[0].node;
    const inventoryItemId = variant.inventoryItem.id;
    const variantId = variant.id;
    const isTracked = variant.inventoryItem.tracked;

    console.log(`Found inventory item ID: ${inventoryItemId} for product ${formattedProductId}`);
    console.log(`Product title: ${result.product.title}, Variant title: ${variant.title}`);
    console.log(`Inventory tracking enabled: ${isTracked}`);

    return {
      inventoryItemId,
      variantId,
      isTracked,
      productTitle: result.product.title,
      variantTitle: variant.title
    };
  } catch (error) {
    console.error(`Error getting inventory item ID for product ${formattedProductId}:`, error);
    throw error;
  }
}

/**
 * Format a product ID to ensure it's in the correct format for the Shopify GraphQL API
 * @param {string|number} productId - The Shopify product ID (can be numeric or gid format)
 * @returns {string} - The formatted product ID
 */
function formatProductId(productId) {
  // If the productId is already in the gid format, return it as is
  if (String(productId).startsWith('gid://shopify/Product/')) {
    return String(productId);
  }

  // Otherwise, format it as a gid
  return `gid://shopify/Product/${productId}`;
}

/**
 * Set inventory quantity to zero for a product using the latest Shopify API
 * @param {string|number} productId - The Shopify product ID (can be numeric or gid://shopify/Product/1234567890)
 * @returns {Promise<Object>} - The result of the operation
 */
/**
 * Find a variant by SKU using the Shopify GraphQL API
 * @param {string} sku - The SKU to search for
 * @returns {Promise<Object|null>} - The variant information or null if not found
 */
export async function findVariantBySku(sku) {
  try {
    console.log(`Finding variant by SKU: ${sku}`);

    // GraphQL query to find a variant by SKU
    const query = `
      query getVariantBySku($query: String!) {
        productVariants(first: 1, query: $query) {
          edges {
            node {
              id
              sku
              inventoryItem {
                id
                tracked
              }
              product {
                id
                title
              }
            }
          }
        }
      }
    `;

    const variables = {
      query: `sku:${sku}`
    };

    const result = await executeShopifyGraphQL(query, variables);

    if (!result.productVariants.edges.length) {
      console.log(`No variant found with SKU: ${sku}`);
      return null;
    }

    const variant = result.productVariants.edges[0].node;

    // Extract the IDs from the GraphQL response
    const variantId = variant.id;
    const productId = variant.product.id;
    const inventoryItemId = variant.inventoryItem.id;
    const isTracked = variant.inventoryItem.tracked;

    console.log(`Found variant with ID: ${variantId} for SKU: ${sku}`);
    console.log(`Product ID: ${productId}, Inventory Item ID: ${inventoryItemId}`);
    console.log(`Inventory tracking enabled: ${isTracked}`);

    return {
      variantId,
      productId,
      inventoryItemId,
      isTracked,
      sku
    };
  } catch (error) {
    console.error(`Error finding variant by SKU ${sku}:`, error);
    return null;
  }
}

/**
 * Find an OSL product in Shopify by SKU
 * @param {string} sku - The OSL SKU to search for (e.g., "OS123")
 * @returns {Promise<Object|null>} - The OSL product information or null if not found
 */
export async function findOslProductInShopify(sku) {
  try {
    console.log(`🔍 Finding Shopify OSL product for variant SKU: ${sku}`);

    const query = `
      query getVariantBySku($query: String!) {
        productVariants(first: 1, query: $query) {
          edges {
            node {
              id
              sku
              product {
                id
                title
                tags
              }
            }
          }
        }
      }
    `;

    const variables = {
      query: `sku:${sku}`
    };

    const result = await executeShopifyGraphQL(query, variables);

    if (!result.productVariants.edges.length) {
      console.log(`❌ No OSL product found for variant SKU: ${sku}`);
      return null;
    }

    const variant = result.productVariants.edges[0].node;
    const productId = variant.product.id.split('/').pop(); // Extract numeric ID

    console.log(`✅ Found OSL product ${productId} (variant ${variant.id}) with SKU ${sku}`);

    return {
      productId,
      variantId: variant.id,
      title: variant.product.title,
      currentTags: variant.product.tags
    };
  } catch (error) {
    console.error(`❌ Error finding OSL product for variant SKU ${sku}:`, error.message);
    return null;
  }
}

export async function setProductInventoryToZero(productId) {
  try {
    // Format the product ID to ensure it's in the correct format
    const formattedProductId = formatProductId(productId);
    console.log(`Setting inventory to zero for product ${productId} (formatted: ${formattedProductId})`);

    // Get the inventory item ID and variant information for the product
    const { inventoryItemId, variantId, isTracked, productTitle, variantTitle } = await getInventoryItemIdForProduct(formattedProductId);

    // If inventory tracking is not enabled, we don't need to update the inventory
    if (!isTracked) {
      console.log(`Inventory tracking is not enabled for product ${formattedProductId}, skipping inventory update`);
      return {
        success: true,
        productId: formattedProductId,
        originalProductId: productId,
        inventoryItemId,
        variantId,
        productTitle,
        variantTitle,
        newQuantity: 0,
        message: "Inventory tracking not enabled, no update needed"
      };
    }

    // Use setInventoryItemQuantity to set the inventory to zero
    const result = await setInventoryItemQuantity(inventoryItemId, 0);

    return {
      ...result,
      productId: formattedProductId,
      originalProductId: productId,
      variantId,
      productTitle,
      variantTitle
    };
  } catch (error) {
    console.error(`Error setting inventory to zero for product ${productId}:`, error);
    throw error;
  }
}
